<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Browser Tools - Settings</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 40px 20px;
      background: #f8f9fa;
      color: #333;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #e9ecef;
    }
    
    .header h1 {
      margin: 0 0 10px 0;
      color: #2c3e50;
      font-size: 28px;
    }
    
    .header p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
    }
    
    .setting-group {
      background: white;
      border-radius: 12px;
      padding: 30px;
      margin-bottom: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
    }
    
    .setting-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #2c3e50;
    }
    
    .setting-description {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 20px;
      line-height: 1.5;
    }
    
    .keybinding-input {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .key-input {
      padding: 12px 16px;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      width: 60px;
      background: #f8f9fa;
      text-transform: uppercase;
      transition: all 0.2s ease;
    }
    
    .key-input:focus {
      outline: none;
      border-color: #007bff;
      background: white;
      box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    }
    
    .key-input.recording {
      border-color: #28a745;
      background: #d4edda;
      animation: pulse 1s infinite;
    }
    
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    .record-btn {
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background 0.2s ease;
    }
    
    .record-btn:hover {
      background: #0056b3;
    }
    
    .record-btn.recording {
      background: #28a745;
    }
    
    .save-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-top: 20px;
    }
    
    .save-btn:hover {
      background: #218838;
      transform: translateY(-1px);
    }
    
    .save-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }
    
    .status-message {
      padding: 12px 16px;
      border-radius: 6px;
      margin-top: 15px;
      font-size: 14px;
      font-weight: 500;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .status-message.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status-message.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status-message.show {
      opacity: 1;
    }
    
    .current-binding {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 6px;
      padding: 10px 15px;
      margin-bottom: 20px;
      font-size: 14px;
      color: #1565c0;
    }
    
    .reset-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      margin-left: 10px;
      transition: background 0.2s ease;
    }
    
    .reset-btn:hover {
      background: #5a6268;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔧 Browser Tools Settings</h1>
    <p>Customize your browser tools experience</p>
  </div>
  
  <div class="setting-group">
    <div class="setting-title">Link Highlighter Keybinding</div>
    <div class="setting-description">
      Choose which key activates the link highlighter. Press the key you want to use, or click "Record Key" and then press your desired key.
    </div>
    
    <div class="current-binding">
      <strong>Current keybinding:</strong> <span id="current-key">L</span>
    </div>
    
    <div class="keybinding-input">
      <input type="text" id="key-input" class="key-input" placeholder="L" readonly>
      <button id="record-btn" class="record-btn">Record Key</button>
      <button id="reset-btn" class="reset-btn">Reset to L</button>
    </div>
    
    <button id="save-btn" class="save-btn">Save Settings</button>
    
    <div id="status-message" class="status-message"></div>
  </div>
  
  <script src="options.js"></script>
</body>
</html>
