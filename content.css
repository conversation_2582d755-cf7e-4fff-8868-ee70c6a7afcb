/* Browser Tools - <PERSON> Highlighter Styles */

/* Page overlay for dimming effect */
.browser-tools-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  z-index: 2147483646 !important;
  pointer-events: none !important;
  transition: opacity 0.2s ease-in-out !important;
}

/* Link highlighting styles */
.browser-tools-highlighted-link {
  position: relative !important;
  z-index: 2147483647 !important;
  background-color: rgba(255, 255, 0, 0.3) !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 0, 0.6) !important;
  border-radius: 2px !important;
  pointer-events: auto !important;
  transition: all 0.2s ease-in-out !important;
}

/* Enhanced hover effect for highlighted links */
.browser-tools-highlighted-link:hover {
  background-color: rgba(255, 255, 0, 0.5) !important;
  box-shadow: 0 0 0 3px rgba(255, 255, 0, 0.8) !important;
  transform: scale(1.02) !important;
}

/* Ensure text remains readable */
.browser-tools-highlighted-link * {
  color: inherit !important;
  text-decoration: inherit !important;
}

/* Animation for smooth transitions */
@keyframes browser-tools-highlight-in {
  from {
    background-color: transparent !important;
    box-shadow: none !important;
  }
  to {
    background-color: rgba(255, 255, 0, 0.3) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 0, 0.6) !important;
  }
}

.browser-tools-highlighted-link {
  animation: browser-tools-highlight-in 0.3s ease-out !important;
}

/* Status indicator */
.browser-tools-status {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 12px !important;
  z-index: 1000000 !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease-in-out !important;
  pointer-events: none !important;
}

.browser-tools-status.show {
  opacity: 1 !important;
}
