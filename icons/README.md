# Extension Icons

This directory should contain the following icon files for the Chrome extension:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Creating Icons

You can create simple icons using any image editor or online icon generator. The icons should represent browser tools, perhaps using symbols like:
- 🔧 Wrench/tools symbol
- 🔗 Link symbol
- ⚡ Lightning bolt for quick tools

## Temporary Solution

For now, you can:
1. Create simple PNG files with the required dimensions
2. Use a solid color background with a simple symbol
3. Or temporarily remove the "icons" section from manifest.json if you want to test without icons

The extension will work without icons, but Chrome will show a default puzzle piece icon instead.
