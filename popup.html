<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .tool {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      border: 1px solid #e0e0e0;
    }
    
    .tool-title {
      font-weight: bold;
      margin-bottom: 5px;
      color: #333;
    }
    
    .tool-description {
      font-size: 12px;
      color: #666;
      margin-bottom: 10px;
    }
    
    .keybinding {
      background: #333;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 11px;
    }
    
    .status {
      color: #28a745;
      font-size: 11px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>🔧 Browser Tools</h2>
    <p style="font-size: 12px; color: #666; margin: 0;">Useful tools for web browsing</p>
  </div>
  
  <div class="tool">
    <div class="tool-title">Link Highlighter</div>
    <div class="tool-description">
      Highlights all links on the page with a yellow tint and dims the background for better visibility.
    </div>
    <div>
      Press <span class="keybinding" id="current-keybinding">L</span> to toggle
    </div>
    <div class="status">✓ Active</div>
  </div>
  
  <div style="text-align: center; margin-top: 20px;">
    <button id="settings-btn" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 12px; cursor: pointer;">
      ⚙️ Settings
    </button>
  </div>

  <div style="text-align: center; margin-top: 10px; font-size: 11px; color: #999;">
    More tools coming soon...
  </div>

  <script src="popup.js"></script>
</body>
</html>
