// Browser Tools - Popup Script

class PopupManager {
  constructor() {
    this.currentKeybindingSpan = document.getElementById('current-keybinding');
    this.settingsBtn = document.getElementById('settings-btn');
    
    this.init();
  }
  
  async init() {
    // Load and display current keybinding
    await this.loadCurrentKeybinding();
    
    // Set up settings button
    this.settingsBtn.addEventListener('click', () => {
      this.openSettings();
    });
  }
  
  async loadCurrentKeybinding() {
    try {
      const result = await chrome.storage.sync.get(['linkHighlighterKey']);
      const currentKey = result.linkHighlighterKey || 'L';
      this.currentKeybindingSpan.textContent = currentKey.toUpperCase();
    } catch (error) {
      console.error('Error loading keybinding:', error);
      this.currentKeybindingSpan.textContent = 'L'; // fallback
    }
  }
  
  openSettings() {
    chrome.runtime.openOptionsPage();
    window.close(); // Close popup
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
