// Browser Tools - Popup Script

class PopupManager {
  constructor() {
    this.currentKeybindingSpan = document.getElementById('current-keybinding');
    this.settingsBtn = document.getElementById('settings-btn');
    
    this.init();
  }
  
  async init() {
    // Load and display current keybinding
    await this.loadCurrentKeybinding();
    
    // Set up settings button
    this.settingsBtn.addEventListener('click', () => {
      this.openSettings();
    });
  }
  
  async loadCurrentKeybinding() {
    try {
      const result = await chrome.storage.sync.get(['linkHighlighterKeybinding']);
      const savedKeybinding = result.linkHighlighterKeybinding;

      if (savedKeybinding) {
        const formattedKeybinding = this.formatKeybinding(savedKeybinding);
        this.currentKeybindingSpan.textContent = formattedKeybinding;
      } else {
        this.currentKeybindingSpan.textContent = 'L'; // fallback
      }
    } catch (error) {
      console.error('Error loading keybinding:', error);
      this.currentKeybindingSpan.textContent = 'L'; // fallback
    }
  }

  formatKeybinding(keybinding) {
    const parts = [];
    if (keybinding.ctrl) parts.push('Ctrl');
    if (keybinding.alt) parts.push('Alt');
    if (keybinding.shift) parts.push('Shift');
    if (keybinding.meta) parts.push('Cmd');
    parts.push(keybinding.key.toUpperCase());
    return parts.join(' + ');
  }
  
  openSettings() {
    chrome.runtime.openOptionsPage();
    window.close(); // Close popup
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
