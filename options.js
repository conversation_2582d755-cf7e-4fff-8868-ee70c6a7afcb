// Browser Tools - Options Page Script

class OptionsManager {
  constructor() {
    this.keyInput = document.getElementById('key-input');
    this.recordBtn = document.getElementById('record-btn');
    this.resetBtn = document.getElementById('reset-btn');
    this.saveBtn = document.getElementById('save-btn');
    this.statusMessage = document.getElementById('status-message');
    this.currentKeySpan = document.getElementById('current-key');
    
    this.isRecording = false;
    this.currentKey = 'l'; // default
    
    this.init();
  }
  
  async init() {
    // Load current settings
    await this.loadSettings();
    
    // Set up event listeners
    this.recordBtn.addEventListener('click', () => this.toggleRecording());
    this.resetBtn.addEventListener('click', () => this.resetToDefault());
    this.saveBtn.addEventListener('click', () => this.saveSettings());
    
    // Listen for key presses when recording
    document.addEventListener('keydown', (event) => this.handleKeyPress(event));
  }
  
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['linkHighlighterKey']);
      this.currentKey = result.linkHighlighterKey || 'l';
      this.updateUI();
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showStatus('Error loading settings', 'error');
    }
  }
  
  updateUI() {
    this.keyInput.value = this.currentKey.toUpperCase();
    this.currentKeySpan.textContent = this.currentKey.toUpperCase();
  }
  
  toggleRecording() {
    if (this.isRecording) {
      this.stopRecording();
    } else {
      this.startRecording();
    }
  }
  
  startRecording() {
    this.isRecording = true;
    this.keyInput.classList.add('recording');
    this.recordBtn.classList.add('recording');
    this.recordBtn.textContent = 'Press a key...';
    this.keyInput.placeholder = 'Press any key';
    this.keyInput.focus();
  }
  
  stopRecording() {
    this.isRecording = false;
    this.keyInput.classList.remove('recording');
    this.recordBtn.classList.remove('recording');
    this.recordBtn.textContent = 'Record Key';
    this.keyInput.blur();
  }
  
  handleKeyPress(event) {
    if (!this.isRecording) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const key = event.key.toLowerCase();
    
    // Validate key (only allow single letters and some special keys)
    if (this.isValidKey(key)) {
      this.currentKey = key;
      this.updateUI();
      this.stopRecording();
      this.showStatus(`Key set to: ${key.toUpperCase()}`, 'success');
    } else {
      this.showStatus('Invalid key. Please use a single letter (A-Z) or space.', 'error');
    }
  }
  
  isValidKey(key) {
    // Allow single letters and space
    return /^[a-z]$/.test(key) || key === ' ';
  }
  
  resetToDefault() {
    this.currentKey = 'l';
    this.updateUI();
    this.showStatus('Reset to default key: L', 'success');
  }
  
  async saveSettings() {
    try {
      this.saveBtn.disabled = true;
      this.saveBtn.textContent = 'Saving...';
      
      await chrome.storage.sync.set({
        linkHighlighterKey: this.currentKey
      });
      
      this.showStatus('Settings saved successfully!', 'success');
      
      // Notify content scripts of the change
      this.notifyContentScripts();
      
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showStatus('Error saving settings', 'error');
    } finally {
      this.saveBtn.disabled = false;
      this.saveBtn.textContent = 'Save Settings';
    }
  }
  
  async notifyContentScripts() {
    try {
      // Get all tabs and send message to update keybinding
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'updateKeybinding',
            key: this.currentKey
          });
        } catch (error) {
          // Ignore errors for tabs that don't have the content script
        }
      }
    } catch (error) {
      console.error('Error notifying content scripts:', error);
    }
  }
  
  showStatus(message, type) {
    this.statusMessage.textContent = message;
    this.statusMessage.className = `status-message ${type}`;
    this.statusMessage.classList.add('show');
    
    // Hide after 3 seconds
    setTimeout(() => {
      this.statusMessage.classList.remove('show');
    }, 3000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new OptionsManager();
});
