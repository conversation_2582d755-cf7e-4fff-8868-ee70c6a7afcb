// Browser Tools - Options Page Script

class OptionsManager {
  constructor() {
    this.keyInput = document.getElementById('key-input');
    this.recordBtn = document.getElementById('record-btn');
    this.resetBtn = document.getElementById('reset-btn');
    this.saveBtn = document.getElementById('save-btn');
    this.statusMessage = document.getElementById('status-message');
    this.currentKeySpan = document.getElementById('current-key');

    // Modifier checkboxes
    this.ctrlCheckbox = document.getElementById('ctrl-modifier');
    this.altCheckbox = document.getElementById('alt-modifier');
    this.shiftCheckbox = document.getElementById('shift-modifier');
    this.metaCheckbox = document.getElementById('meta-modifier');

    this.isRecording = false;
    this.currentKeybinding = {
      key: 'l',
      ctrl: false,
      alt: false,
      shift: false,
      meta: false
    };

    this.init();
  }
  
  async init() {
    // Load current settings
    await this.loadSettings();
    
    // Set up event listeners
    this.recordBtn.addEventListener('click', () => this.toggleRecording());
    this.resetBtn.addEventListener('click', () => this.resetToDefault());
    this.saveBtn.addEventListener('click', () => this.saveSettings());
    
    // Listen for key presses when recording
    document.addEventListener('keydown', (event) => this.handleKeyPress(event));
  }
  
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['linkHighlighterKeybinding']);
      const savedKeybinding = result.linkHighlighterKeybinding;

      if (savedKeybinding) {
        this.currentKeybinding = {
          key: savedKeybinding.key || 'l',
          ctrl: savedKeybinding.ctrl || false,
          alt: savedKeybinding.alt || false,
          shift: savedKeybinding.shift || false,
          meta: savedKeybinding.meta || false
        };
      }

      this.updateUI();
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showStatus('Error loading settings', 'error');
    }
  }

  updateUI() {
    this.keyInput.value = this.currentKeybinding.key.toUpperCase();
    this.ctrlCheckbox.checked = this.currentKeybinding.ctrl;
    this.altCheckbox.checked = this.currentKeybinding.alt;
    this.shiftCheckbox.checked = this.currentKeybinding.shift;
    this.metaCheckbox.checked = this.currentKeybinding.meta;

    this.currentKeySpan.textContent = this.formatKeybinding(this.currentKeybinding);
  }

  formatKeybinding(keybinding) {
    const parts = [];
    if (keybinding.ctrl) parts.push('Ctrl');
    if (keybinding.alt) parts.push('Alt');
    if (keybinding.shift) parts.push('Shift');
    if (keybinding.meta) parts.push('Cmd');
    parts.push(keybinding.key.toUpperCase());
    return parts.join(' + ');
  }
  
  toggleRecording() {
    if (this.isRecording) {
      this.stopRecording();
    } else {
      this.startRecording();
    }
  }
  
  startRecording() {
    this.isRecording = true;
    this.keyInput.classList.add('recording');
    this.recordBtn.classList.add('recording');
    this.recordBtn.textContent = 'Press a key...';
    this.keyInput.placeholder = 'Press any key';
    this.keyInput.focus();
  }
  
  stopRecording() {
    this.isRecording = false;
    this.keyInput.classList.remove('recording');
    this.recordBtn.classList.remove('recording');
    this.recordBtn.textContent = 'Record Key';
    this.keyInput.blur();
  }
  
  handleKeyPress(event) {
    if (!this.isRecording) return;

    event.preventDefault();
    event.stopPropagation();

    const key = event.key.toLowerCase();

    // Skip if it's just a modifier key by itself
    if (['control', 'alt', 'shift', 'meta'].includes(key)) {
      return;
    }

    // Validate key (only allow single letters and some special keys)
    if (this.isValidKey(key)) {
      this.currentKeybinding = {
        key: key,
        ctrl: event.ctrlKey,
        alt: event.altKey,
        shift: event.shiftKey,
        meta: event.metaKey
      };

      this.updateUI();
      this.stopRecording();
      this.showStatus(`Keybinding set to: ${this.formatKeybinding(this.currentKeybinding)}`, 'success');
    } else {
      this.showStatus('Invalid key. Please use a single letter (A-Z) or space.', 'error');
    }
  }
  
  isValidKey(key) {
    // Allow single letters and space
    return /^[a-z]$/.test(key) || key === ' ';
  }
  
  resetToDefault() {
    this.currentKeybinding = {
      key: 'l',
      ctrl: false,
      alt: false,
      shift: false,
      meta: false
    };
    this.updateUI();
    this.showStatus('Reset to default keybinding: L', 'success');
  }

  async saveSettings() {
    try {
      this.saveBtn.disabled = true;
      this.saveBtn.textContent = 'Saving...';

      // Also update from checkboxes in case user changed them manually
      this.currentKeybinding.ctrl = this.ctrlCheckbox.checked;
      this.currentKeybinding.alt = this.altCheckbox.checked;
      this.currentKeybinding.shift = this.shiftCheckbox.checked;
      this.currentKeybinding.meta = this.metaCheckbox.checked;

      await chrome.storage.sync.set({
        linkHighlighterKeybinding: this.currentKeybinding
      });

      this.showStatus('Settings saved successfully!', 'success');

      // Notify content scripts of the change
      this.notifyContentScripts();

    } catch (error) {
      console.error('Error saving settings:', error);
      this.showStatus('Error saving settings', 'error');
    } finally {
      this.saveBtn.disabled = false;
      this.saveBtn.textContent = 'Save Settings';
    }
  }
  
  async notifyContentScripts() {
    try {
      // Get all tabs and send message to update keybinding
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'updateKeybinding',
            keybinding: this.currentKeybinding
          });
        } catch (error) {
          // Ignore errors for tabs that don't have the content script
        }
      }
    } catch (error) {
      console.error('Error notifying content scripts:', error);
    }
  }
  
  showStatus(message, type) {
    this.statusMessage.textContent = message;
    this.statusMessage.className = `status-message ${type}`;
    this.statusMessage.classList.add('show');
    
    // Hide after 3 seconds
    setTimeout(() => {
      this.statusMessage.classList.remove('show');
    }, 3000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new OptionsManager();
});
