# Browser Tools Chrome Extension

A collection of useful browser tools to enhance your web browsing experience.

## Features

### 🔗 Link Highlighter
- **Keybinding**: Press `L` to toggle
- **Function**: Highlights all clickable links on the page with a yellow tint
- **Effect**: Dims the rest of the page with a dark overlay while keeping links clickable
- **Exit**: Press `L` again or `ESC` to disable

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select this extension directory
4. The Browser Tools extension should now appear in your extensions list

## Usage

### Link Highlighter
1. Navigate to any webpage
2. Press the `L` key to activate link highlighting
3. All clickable elements (links, buttons, etc.) will be highlighted with a yellow tint
4. The rest of the page will be dimmed with a dark overlay
5. Links remain fully clickable while highlighted
6. Press `L` again or `ESC` to disable the highlighting

## Technical Details

- **Manifest Version**: 3
- **Permissions**: `activeTab`, `scripting`
- **Content Scripts**: Injected into all pages
- **Popup**: Shows available tools and their keybindings

## Files Structure

```
browser-tools/
├── manifest.json          # Extension configuration
├── popup.html            # Extension popup interface
├── content.js            # Main content script with tools
├── content.css           # Styling for tools
├── icons/                # Extension icons
└── README.md            # This file
```

## Future Tools

This extension is designed to house multiple browser tools. Future additions may include:
- Text selection tools
- Page analysis tools
- Navigation helpers
- Accessibility tools

## Development

The extension uses modern Chrome Extension Manifest V3 standards and is built with vanilla JavaScript for maximum compatibility and performance.
