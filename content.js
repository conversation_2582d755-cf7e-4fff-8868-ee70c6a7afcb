// Browser Tools - Content Script
// Link Highlighter Tool

class BrowserTools {
  constructor() {
    this.isLinkHighlightActive = false;
    this.overlay = null;
    this.statusIndicator = null;
    this.highlightedLinks = [];
    this.highlightOverlays = [];
    this.updateAnimationId = null;

    this.init();
  }
  
  init() {
    // Listen for keydown events
    document.addEventListener('keydown', (event) => {
      this.handleKeyPress(event);
    });

    // Listen for clicks to exit highlighting when a link is clicked
    document.addEventListener('click', (event) => {
      this.handleClick(event);
    });
  }
  
  handleKeyPress(event) {
    // Check if 'L' key is pressed (case insensitive)
    if (event.key.toLowerCase() === 'l') {
      // Don't trigger if user is typing in an input field
      const activeElement = document.activeElement;
      const isTyping = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true'
      );

      if (!isTyping) {
        event.preventDefault();
        this.toggleLinkHighlight();
      }
    }

    // ESC key to disable highlight
    if (event.key === 'Escape' && this.isLinkHighlightActive) {
      event.preventDefault();
      this.disableLinkHighlight();
    }
  }

  handleClick(event) {
    // If highlighting is active and a highlighted link is clicked, disable highlighting
    if (this.isLinkHighlightActive) {
      const clickedElement = event.target;

      // Check if the clicked element or any of its parents is a highlighted link
      let element = clickedElement;
      while (element && element !== document.body) {
        if (element.classList && element.classList.contains('browser-tools-highlighted-link')) {
          // Disable highlighting immediately when a link is clicked
          this.disableLinkHighlight();
          break;
        }
        element = element.parentElement;
      }
    }
  }
  
  toggleLinkHighlight() {
    if (this.isLinkHighlightActive) {
      this.disableLinkHighlight();
    } else {
      this.enableLinkHighlight();
    }
  }
  
  enableLinkHighlight() {
    this.isLinkHighlightActive = true;

    // Create overlay for dimming effect
    this.createOverlay();

    // Find and highlight all links
    this.highlightAllLinks();

    // Start position update loop
    this.startUpdateLoop();

    // Show status indicator
    this.showStatus('Link Highlighter: ON (Press L or ESC to toggle)');
  }
  
  disableLinkHighlight() {
    this.isLinkHighlightActive = false;

    // Stop position update loop
    this.stopUpdateLoop();

    // Remove overlay
    this.removeOverlay();

    // Remove link highlights
    this.removeAllHighlights();

    // Show status indicator
    this.showStatus('Link Highlighter: OFF');
  }
  
  createOverlay() {
    if (this.overlay) return;
    
    this.overlay = document.createElement('div');
    this.overlay.className = 'browser-tools-overlay';
    document.body.appendChild(this.overlay);
  }
  
  removeOverlay() {
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }
  }
  
  highlightAllLinks() {
    // Find all clickable elements (links, buttons, etc.)
    const selectors = [
      'a[href]',
      'button',
      '[onclick]',
      '[role="button"]',
      '[role="link"]',
      'input[type="button"]',
      'input[type="submit"]',
      'input[type="reset"]'
    ];

    const clickableElements = document.querySelectorAll(selectors.join(', '));

    clickableElements.forEach(element => {
      // Skip if already highlighted
      if (element.classList.contains('browser-tools-highlighted-link')) {
        return;
      }

      // Skip if element is not visible
      if (!this.isElementVisible(element)) {
        return;
      }

      // Add class to the element for identification
      element.classList.add('browser-tools-highlighted-link');
      this.highlightedLinks.push(element);

      // Create individual overlay for this element
      this.createLinkOverlay(element);
    });
  }
  
  createLinkOverlay(element) {
    const rect = element.getBoundingClientRect();
    const overlay = document.createElement('div');
    overlay.className = 'browser-tools-highlight-overlay';

    // Position the overlay exactly over the element
    overlay.style.left = rect.left + 'px';
    overlay.style.top = rect.top + 'px';
    overlay.style.width = rect.width + 'px';
    overlay.style.height = rect.height + 'px';

    document.body.appendChild(overlay);
    this.highlightOverlays.push({ overlay, element });
  }

  startUpdateLoop() {
    const updatePositions = () => {
      if (this.isLinkHighlightActive) {
        this.highlightOverlays.forEach(({ overlay, element }) => {
          if (document.body.contains(overlay) && document.body.contains(element)) {
            const rect = element.getBoundingClientRect();
            overlay.style.left = rect.left + 'px';
            overlay.style.top = rect.top + 'px';
            overlay.style.width = rect.width + 'px';
            overlay.style.height = rect.height + 'px';
          }
        });
        this.updateAnimationId = requestAnimationFrame(updatePositions);
      }
    };
    this.updateAnimationId = requestAnimationFrame(updatePositions);
  }

  stopUpdateLoop() {
    if (this.updateAnimationId) {
      cancelAnimationFrame(this.updateAnimationId);
      this.updateAnimationId = null;
    }
  }

  removeAllHighlights() {
    this.highlightedLinks.forEach(element => {
      element.classList.remove('browser-tools-highlighted-link');
    });
    this.highlightedLinks = [];

    // Remove all highlight overlays
    this.highlightOverlays.forEach(({ overlay }) => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    });
    this.highlightOverlays = [];
  }
  
  isElementVisible(element) {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.visibility !== 'hidden' &&
      style.display !== 'none' &&
      style.opacity !== '0'
    );
  }
  
  showStatus(message) {
    // Remove existing status indicator
    if (this.statusIndicator) {
      this.statusIndicator.remove();
    }
    
    // Create new status indicator
    this.statusIndicator = document.createElement('div');
    this.statusIndicator.className = 'browser-tools-status';
    this.statusIndicator.textContent = message;
    document.body.appendChild(this.statusIndicator);
    
    // Show with animation
    setTimeout(() => {
      this.statusIndicator.classList.add('show');
    }, 10);
    
    // Hide after 3 seconds
    setTimeout(() => {
      if (this.statusIndicator) {
        this.statusIndicator.classList.remove('show');
        setTimeout(() => {
          if (this.statusIndicator) {
            this.statusIndicator.remove();
            this.statusIndicator = null;
          }
        }, 300);
      }
    }, 3000);
  }
}

// Initialize the Browser Tools when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new BrowserTools();
  });
} else {
  new BrowserTools();
}
