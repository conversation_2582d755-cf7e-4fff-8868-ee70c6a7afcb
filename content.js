// Browser Tools - Content Script
// Link Highlighter Tool

class BrowserTools {
  constructor() {
    this.isLinkHighlightActive = false;
    this.overlay = null;
    this.statusIndicator = null;
    this.highlightedLinks = [];
    
    this.init();
  }
  
  init() {
    // Listen for keydown events
    document.addEventListener('keydown', (event) => {
      this.handleKeyPress(event);
    });

    console.log('Browser Tools: Content script initialized');
  }
  
  handleKeyPress(event) {
    // Check if 'L' key is pressed (case insensitive)
    if (event.key.toLowerCase() === 'l') {
      // Don't trigger if user is typing in an input field
      const activeElement = document.activeElement;
      const isTyping = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true'
      );
      
      if (!isTyping) {
        event.preventDefault();
        this.toggleLinkHighlight();
      }
    }
    
    // ESC key to disable highlight
    if (event.key === 'Escape' && this.isLinkHighlightActive) {
      event.preventDefault();
      this.disableLinkHighlight();
    }
  }
  
  toggleLinkHighlight() {
    if (this.isLinkHighlightActive) {
      this.disableLinkHighlight();
    } else {
      this.enableLinkHighlight();
    }
  }
  
  enableLinkHighlight() {
    this.isLinkHighlightActive = true;
    
    // Create overlay for dimming effect
    this.createOverlay();
    
    // Find and highlight all links
    this.highlightAllLinks();
    
    // Show status indicator
    this.showStatus('Link Highlighter: ON (Press L or ESC to toggle)');
    
    console.log('Browser Tools: Link highlighting enabled');
  }
  
  disableLinkHighlight() {
    this.isLinkHighlightActive = false;
    
    // Remove overlay
    this.removeOverlay();
    
    // Remove link highlights
    this.removeAllHighlights();
    
    // Show status indicator
    this.showStatus('Link Highlighter: OFF');
    
    console.log('Browser Tools: Link highlighting disabled');
  }
  
  createOverlay() {
    if (this.overlay) return;
    
    this.overlay = document.createElement('div');
    this.overlay.className = 'browser-tools-overlay';
    document.body.appendChild(this.overlay);
  }
  
  removeOverlay() {
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }
  }
  
  highlightAllLinks() {
    // Find all clickable elements (links, buttons, etc.)
    const selectors = [
      'a[href]',
      'button',
      '[onclick]',
      '[role="button"]',
      '[role="link"]',
      'input[type="button"]',
      'input[type="submit"]',
      'input[type="reset"]'
    ];
    
    const clickableElements = document.querySelectorAll(selectors.join(', '));
    
    clickableElements.forEach(element => {
      // Skip if already highlighted
      if (element.classList.contains('browser-tools-highlighted-link')) {
        return;
      }
      
      // Skip if element is not visible
      if (!this.isElementVisible(element)) {
        return;
      }
      
      element.classList.add('browser-tools-highlighted-link');
      this.highlightedLinks.push(element);
    });
  }
  
  removeAllHighlights() {
    this.highlightedLinks.forEach(element => {
      element.classList.remove('browser-tools-highlighted-link');
    });
    this.highlightedLinks = [];
  }
  
  isElementVisible(element) {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.visibility !== 'hidden' &&
      style.display !== 'none' &&
      style.opacity !== '0'
    );
  }
  
  showStatus(message) {
    // Remove existing status indicator
    if (this.statusIndicator) {
      this.statusIndicator.remove();
    }
    
    // Create new status indicator
    this.statusIndicator = document.createElement('div');
    this.statusIndicator.className = 'browser-tools-status';
    this.statusIndicator.textContent = message;
    document.body.appendChild(this.statusIndicator);
    
    // Show with animation
    setTimeout(() => {
      this.statusIndicator.classList.add('show');
    }, 10);
    
    // Hide after 3 seconds
    setTimeout(() => {
      if (this.statusIndicator) {
        this.statusIndicator.classList.remove('show');
        setTimeout(() => {
          if (this.statusIndicator) {
            this.statusIndicator.remove();
            this.statusIndicator = null;
          }
        }, 300);
      }
    }, 3000);
  }
}

// Initialize the Browser Tools when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new BrowserTools();
  });
} else {
  new BrowserTools();
}
